version: '3.8'

services:
  postgres:
    image: postgres:13
    container_name: userauth-postgres
    environment:
      POSTGRES_DB: userauth
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - userauth-network

  app:
    build: .
    container_name: userauth-app
    depends_on:
      - postgres
    environment:
      SPRING_DATASOURCE_URL: ****************************************
      SPRING_DATASOURCE_USERNAME: postgres
      SPRING_DATASOURCE_PASSWORD: password
    ports:
      - "8080:8080"
    networks:
      - userauth-network

volumes:
  postgres_data:

networks:
  userauth-network:
    driver: bridge
