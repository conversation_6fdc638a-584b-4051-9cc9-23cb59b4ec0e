version: '3.3'

volumes:
  userauth_db_data:

networks:
  userauth-net:
    external: false

services:
  userauth-db:
    image: postgres:13
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_DB: userauth
    ports:
      - "5433:5432"
    healthcheck:
      test: [ "CMD-SHELL", "pg_isready -U postgres" ]
      interval: 30s
      timeout: 30s
      retries: 3
    restart: on-failure
    deploy:
      restart_policy:
        condition: on-failure
    stdin_open: true
    tty: true
    networks:
      - userauth-net
    volumes:
      - userauth_db_data:/var/lib/postgresql/data
