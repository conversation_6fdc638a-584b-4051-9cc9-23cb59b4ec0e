/*
 * This file is generated by jOOQ.
 */
package com.example.userauth.db.jooq.tables.pojos;


import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.UUID;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Users implements Serializable {

    private static final long serialVersionUID = 1L;

    private UUID          id;
    private String        username;
    private String        email;
    private String        passwordHash;
    private String        firstName;
    private String        lastName;
    private Boolean       isActive;
    private String        resetToken;
    private LocalDateTime resetTokenExpiry;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    public Users() {}

    public Users(Users value) {
        this.id = value.id;
        this.username = value.username;
        this.email = value.email;
        this.passwordHash = value.passwordHash;
        this.firstName = value.firstName;
        this.lastName = value.lastName;
        this.isActive = value.isActive;
        this.resetToken = value.resetToken;
        this.resetTokenExpiry = value.resetTokenExpiry;
        this.createdAt = value.createdAt;
        this.updatedAt = value.updatedAt;
    }

    public Users(
        UUID          id,
        String        username,
        String        email,
        String        passwordHash,
        String        firstName,
        String        lastName,
        Boolean       isActive,
        String        resetToken,
        LocalDateTime resetTokenExpiry,
        LocalDateTime createdAt,
        LocalDateTime updatedAt
    ) {
        this.id = id;
        this.username = username;
        this.email = email;
        this.passwordHash = passwordHash;
        this.firstName = firstName;
        this.lastName = lastName;
        this.isActive = isActive;
        this.resetToken = resetToken;
        this.resetTokenExpiry = resetTokenExpiry;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
    }

    /**
     * Getter for <code>public.users.id</code>.
     */
    public UUID getId() {
        return this.id;
    }

    /**
     * Setter for <code>public.users.id</code>.
     */
    public void setId(UUID id) {
        this.id = id;
    }

    /**
     * Getter for <code>public.users.username</code>.
     */
    public String getUsername() {
        return this.username;
    }

    /**
     * Setter for <code>public.users.username</code>.
     */
    public void setUsername(String username) {
        this.username = username;
    }

    /**
     * Getter for <code>public.users.email</code>.
     */
    public String getEmail() {
        return this.email;
    }

    /**
     * Setter for <code>public.users.email</code>.
     */
    public void setEmail(String email) {
        this.email = email;
    }

    /**
     * Getter for <code>public.users.password_hash</code>.
     */
    public String getPasswordHash() {
        return this.passwordHash;
    }

    /**
     * Setter for <code>public.users.password_hash</code>.
     */
    public void setPasswordHash(String passwordHash) {
        this.passwordHash = passwordHash;
    }

    /**
     * Getter for <code>public.users.first_name</code>.
     */
    public String getFirstName() {
        return this.firstName;
    }

    /**
     * Setter for <code>public.users.first_name</code>.
     */
    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    /**
     * Getter for <code>public.users.last_name</code>.
     */
    public String getLastName() {
        return this.lastName;
    }

    /**
     * Setter for <code>public.users.last_name</code>.
     */
    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    /**
     * Getter for <code>public.users.is_active</code>.
     */
    public Boolean getIsActive() {
        return this.isActive;
    }

    /**
     * Setter for <code>public.users.is_active</code>.
     */
    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }

    /**
     * Getter for <code>public.users.reset_token</code>.
     */
    public String getResetToken() {
        return this.resetToken;
    }

    /**
     * Setter for <code>public.users.reset_token</code>.
     */
    public void setResetToken(String resetToken) {
        this.resetToken = resetToken;
    }

    /**
     * Getter for <code>public.users.reset_token_expiry</code>.
     */
    public LocalDateTime getResetTokenExpiry() {
        return this.resetTokenExpiry;
    }

    /**
     * Setter for <code>public.users.reset_token_expiry</code>.
     */
    public void setResetTokenExpiry(LocalDateTime resetTokenExpiry) {
        this.resetTokenExpiry = resetTokenExpiry;
    }

    /**
     * Getter for <code>public.users.created_at</code>.
     */
    public LocalDateTime getCreatedAt() {
        return this.createdAt;
    }

    /**
     * Setter for <code>public.users.created_at</code>.
     */
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    /**
     * Getter for <code>public.users.updated_at</code>.
     */
    public LocalDateTime getUpdatedAt() {
        return this.updatedAt;
    }

    /**
     * Setter for <code>public.users.updated_at</code>.
     */
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("Users (");

        sb.append(id);
        sb.append(", ").append(username);
        sb.append(", ").append(email);
        sb.append(", ").append(passwordHash);
        sb.append(", ").append(firstName);
        sb.append(", ").append(lastName);
        sb.append(", ").append(isActive);
        sb.append(", ").append(resetToken);
        sb.append(", ").append(resetTokenExpiry);
        sb.append(", ").append(createdAt);
        sb.append(", ").append(updatedAt);

        sb.append(")");
        return sb.toString();
    }
}
