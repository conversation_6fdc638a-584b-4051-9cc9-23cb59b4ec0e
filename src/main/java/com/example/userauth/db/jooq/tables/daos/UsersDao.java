/*
 * This file is generated by jOOQ.
 */
package com.example.userauth.db.jooq.tables.daos;


import com.example.userauth.db.jooq.tables.Users;
import com.example.userauth.db.jooq.tables.records.UsersRecord;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import org.jooq.Configuration;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
@Repository
public class UsersDao extends DAOImpl<UsersRecord, com.example.userauth.db.jooq.tables.pojos.Users, UUID> {

    /**
     * Create a new UsersDao without any configuration
     */
    public UsersDao() {
        super(Users.USERS, com.example.userauth.db.jooq.tables.pojos.Users.class);
    }

    /**
     * Create a new UsersDao with an attached configuration
     */
    @Autowired
    public UsersDao(Configuration configuration) {
        super(Users.USERS, com.example.userauth.db.jooq.tables.pojos.Users.class, configuration);
    }

    @Override
    public UUID getId(com.example.userauth.db.jooq.tables.pojos.Users object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.example.userauth.db.jooq.tables.pojos.Users> fetchRangeOfId(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(Users.USERS.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<com.example.userauth.db.jooq.tables.pojos.Users> fetchById(UUID... values) {
        return fetch(Users.USERS.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public com.example.userauth.db.jooq.tables.pojos.Users fetchOneById(UUID value) {
        return fetchOne(Users.USERS.ID, value);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public Optional<com.example.userauth.db.jooq.tables.pojos.Users> fetchOptionalById(UUID value) {
        return fetchOptional(Users.USERS.ID, value);
    }

    /**
     * Fetch records that have <code>username BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.example.userauth.db.jooq.tables.pojos.Users> fetchRangeOfUsername(String lowerInclusive, String upperInclusive) {
        return fetchRange(Users.USERS.USERNAME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>username IN (values)</code>
     */
    public List<com.example.userauth.db.jooq.tables.pojos.Users> fetchByUsername(String... values) {
        return fetch(Users.USERS.USERNAME, values);
    }

    /**
     * Fetch a unique record that has <code>username = value</code>
     */
    public com.example.userauth.db.jooq.tables.pojos.Users fetchOneByUsername(String value) {
        return fetchOne(Users.USERS.USERNAME, value);
    }

    /**
     * Fetch a unique record that has <code>username = value</code>
     */
    public Optional<com.example.userauth.db.jooq.tables.pojos.Users> fetchOptionalByUsername(String value) {
        return fetchOptional(Users.USERS.USERNAME, value);
    }

    /**
     * Fetch records that have <code>email BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.example.userauth.db.jooq.tables.pojos.Users> fetchRangeOfEmail(String lowerInclusive, String upperInclusive) {
        return fetchRange(Users.USERS.EMAIL, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>email IN (values)</code>
     */
    public List<com.example.userauth.db.jooq.tables.pojos.Users> fetchByEmail(String... values) {
        return fetch(Users.USERS.EMAIL, values);
    }

    /**
     * Fetch a unique record that has <code>email = value</code>
     */
    public com.example.userauth.db.jooq.tables.pojos.Users fetchOneByEmail(String value) {
        return fetchOne(Users.USERS.EMAIL, value);
    }

    /**
     * Fetch a unique record that has <code>email = value</code>
     */
    public Optional<com.example.userauth.db.jooq.tables.pojos.Users> fetchOptionalByEmail(String value) {
        return fetchOptional(Users.USERS.EMAIL, value);
    }

    /**
     * Fetch records that have <code>password_hash BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.example.userauth.db.jooq.tables.pojos.Users> fetchRangeOfPasswordHash(String lowerInclusive, String upperInclusive) {
        return fetchRange(Users.USERS.PASSWORD_HASH, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>password_hash IN (values)</code>
     */
    public List<com.example.userauth.db.jooq.tables.pojos.Users> fetchByPasswordHash(String... values) {
        return fetch(Users.USERS.PASSWORD_HASH, values);
    }

    /**
     * Fetch records that have <code>first_name BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.example.userauth.db.jooq.tables.pojos.Users> fetchRangeOfFirstName(String lowerInclusive, String upperInclusive) {
        return fetchRange(Users.USERS.FIRST_NAME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>first_name IN (values)</code>
     */
    public List<com.example.userauth.db.jooq.tables.pojos.Users> fetchByFirstName(String... values) {
        return fetch(Users.USERS.FIRST_NAME, values);
    }

    /**
     * Fetch records that have <code>last_name BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.example.userauth.db.jooq.tables.pojos.Users> fetchRangeOfLastName(String lowerInclusive, String upperInclusive) {
        return fetchRange(Users.USERS.LAST_NAME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>last_name IN (values)</code>
     */
    public List<com.example.userauth.db.jooq.tables.pojos.Users> fetchByLastName(String... values) {
        return fetch(Users.USERS.LAST_NAME, values);
    }

    /**
     * Fetch records that have <code>is_active BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.example.userauth.db.jooq.tables.pojos.Users> fetchRangeOfIsActive(Boolean lowerInclusive, Boolean upperInclusive) {
        return fetchRange(Users.USERS.IS_ACTIVE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_active IN (values)</code>
     */
    public List<com.example.userauth.db.jooq.tables.pojos.Users> fetchByIsActive(Boolean... values) {
        return fetch(Users.USERS.IS_ACTIVE, values);
    }

    /**
     * Fetch records that have <code>reset_token BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.example.userauth.db.jooq.tables.pojos.Users> fetchRangeOfResetToken(String lowerInclusive, String upperInclusive) {
        return fetchRange(Users.USERS.RESET_TOKEN, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>reset_token IN (values)</code>
     */
    public List<com.example.userauth.db.jooq.tables.pojos.Users> fetchByResetToken(String... values) {
        return fetch(Users.USERS.RESET_TOKEN, values);
    }

    /**
     * Fetch records that have <code>reset_token_expiry BETWEEN lowerInclusive
     * AND upperInclusive</code>
     */
    public List<com.example.userauth.db.jooq.tables.pojos.Users> fetchRangeOfResetTokenExpiry(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(Users.USERS.RESET_TOKEN_EXPIRY, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>reset_token_expiry IN (values)</code>
     */
    public List<com.example.userauth.db.jooq.tables.pojos.Users> fetchByResetTokenExpiry(LocalDateTime... values) {
        return fetch(Users.USERS.RESET_TOKEN_EXPIRY, values);
    }

    /**
     * Fetch records that have <code>created_at BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.example.userauth.db.jooq.tables.pojos.Users> fetchRangeOfCreatedAt(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(Users.USERS.CREATED_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_at IN (values)</code>
     */
    public List<com.example.userauth.db.jooq.tables.pojos.Users> fetchByCreatedAt(LocalDateTime... values) {
        return fetch(Users.USERS.CREATED_AT, values);
    }

    /**
     * Fetch records that have <code>updated_at BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.example.userauth.db.jooq.tables.pojos.Users> fetchRangeOfUpdatedAt(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(Users.USERS.UPDATED_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>updated_at IN (values)</code>
     */
    public List<com.example.userauth.db.jooq.tables.pojos.Users> fetchByUpdatedAt(LocalDateTime... values) {
        return fetch(Users.USERS.UPDATED_AT, values);
    }
}
