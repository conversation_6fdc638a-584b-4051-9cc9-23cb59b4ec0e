package com.example.userauth.db.jooq;

import org.jooq.Field;
import org.jooq.Table;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Temporary Tables class until j<PERSON><PERSON><PERSON> generates the actual one
 * This will be replaced by jOOQ generated code after running generateJooq task
 */
public class Tables {
    
    public static final UsersTable USERS = new UsersTable();
    
    public static class UsersTable extends org.jooq.impl.TableImpl<org.jooq.Record> {
        
        public final Field<UUID> ID = createField(DSL.name("id"), SQLDataType.UUID, this, "");
        public final Field<String> USERNAME = createField(DSL.name("username"), SQLDataType.VARCHAR(50), this, "");
        public final Field<String> EMAIL = createField(DSL.name("email"), SQLDataType.VARCHAR(100), this, "");
        public final Field<String> PASSWORD_HASH = createField(DSL.name("password_hash"), SQLDataType.VARCHAR(255), this, "");
        public final Field<String> FIRST_NAME = createField(DSL.name("first_name"), SQLDataType.VARCHAR(50), this, "");
        public final Field<String> LAST_NAME = createField(DSL.name("last_name"), SQLDataType.VARCHAR(50), this, "");
        public final Field<Boolean> IS_ACTIVE = createField(DSL.name("is_active"), SQLDataType.BOOLEAN, this, "");
        public final Field<String> RESET_TOKEN = createField(DSL.name("reset_token"), SQLDataType.VARCHAR(255), this, "");
        public final Field<LocalDateTime> RESET_TOKEN_EXPIRY = createField(DSL.name("reset_token_expiry"), SQLDataType.LOCALDATETIME, this, "");
        public final Field<LocalDateTime> CREATED_AT = createField(DSL.name("created_at"), SQLDataType.LOCALDATETIME, this, "");
        public final Field<LocalDateTime> UPDATED_AT = createField(DSL.name("updated_at"), SQLDataType.LOCALDATETIME, this, "");
        
        public UsersTable() {
            super(DSL.name("users"));
        }
        
        @Override
        public Table<org.jooq.Record> as(String alias) {
            return new UsersTable().rename(alias);
        }
    }
}
