package com.example.userauth.service;

import com.example.userauth.dto.request.ForgotPasswordRequest;
import com.example.userauth.dto.request.UserLoginRequest;
import com.example.userauth.dto.request.UserRegistrationRequest;
import com.example.userauth.dto.response.ForgotPasswordResponse;
import com.example.userauth.dto.response.UserLoginResponse;
import com.example.userauth.dto.response.UserRegistrationResponse;
import com.example.userauth.repository.UserRepository;
import com.example.userauth.security.JwtTokenProvider;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jooq.Record;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Optional;
import java.util.UUID;

import static com.example.userauth.db.jooq.Tables.USERS;

/**
 * Service layer for user operations
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class UserService {

    private final UserRepository userRepository;
    private final PasswordEncoder passwordEncoder;
    private final JwtTokenProvider jwtTokenProvider;
    private final JavaMailSender mailSender;

    /**
     * Register a new user
     */
    @Transactional
    public UserRegistrationResponse registerUser(UserRegistrationRequest request) {
        log.info("Registering new user with username: {}", request.getUsername());

        // Check if username already exists
        if (userRepository.existsByUsername(request.getUsername())) {
            throw new RuntimeException("Username already exists");
        }

        // Check if email already exists
        if (userRepository.existsByEmail(request.getEmail())) {
            throw new RuntimeException("Email already exists");
        }

        // Encode password
        String encodedPassword = passwordEncoder.encode(request.getPassword());

        // Save user
        UUID userId = userRepository.saveUser(
                request.getUsername(),
                request.getEmail(),
                encodedPassword,
                request.getFirstName(),
                request.getLastName()
        );

        // Fetch the saved user to get all details
        Optional<Record> userRecord = userRepository.findById(userId);
        if (userRecord.isEmpty()) {
            throw new RuntimeException("Failed to retrieve saved user");
        }

        Record user = userRecord.get();
        
        return UserRegistrationResponse.builder()
                .id(user.get(USERS.ID))
                .username(user.get(USERS.USERNAME))
                .email(user.get(USERS.EMAIL))
                .firstName(user.get(USERS.FIRST_NAME))
                .lastName(user.get(USERS.LAST_NAME))
                .isActive(user.get(USERS.IS_ACTIVE))
                .createdAt(user.get(USERS.CREATED_AT))
                .message("User registered successfully")
                .build();
    }

    /**
     * Authenticate user and generate JWT token
     */
    public UserLoginResponse loginUser(UserLoginRequest request) {
        log.info("Attempting login for user: {}", request.getUsernameOrEmail());

        // Find user by username or email
        Optional<Record> userRecord = userRepository.findByUsernameOrEmail(request.getUsernameOrEmail());
        if (userRecord.isEmpty()) {
            throw new RuntimeException("Invalid credentials");
        }

        Record user = userRecord.get();
        
        // Verify password
        if (!passwordEncoder.matches(request.getPassword(), user.get(USERS.PASSWORD_HASH))) {
            throw new RuntimeException("Invalid credentials");
        }

        // Generate JWT token
        String token = jwtTokenProvider.generateToken(user.get(USERS.USERNAME));
        Long expirationTime = jwtTokenProvider.getExpirationTime();

        log.info("User logged in successfully: {}", user.get(USERS.USERNAME));

        return UserLoginResponse.builder()
                .userId(user.get(USERS.ID))
                .username(user.get(USERS.USERNAME))
                .email(user.get(USERS.EMAIL))
                .firstName(user.get(USERS.FIRST_NAME))
                .lastName(user.get(USERS.LAST_NAME))
                .accessToken(token)
                .tokenType("Bearer")
                .expiresIn(expirationTime)
                .message("Login successful")
                .build();
    }

    /**
     * Handle forgot password request
     */
    @Transactional
    public ForgotPasswordResponse forgotPassword(ForgotPasswordRequest request) {
        log.info("Processing forgot password request for email: {}", request.getEmail());

        // Check if user exists
        Optional<Record> userRecord = userRepository.findByEmail(request.getEmail());
        if (userRecord.isEmpty()) {
            // For security reasons, don't reveal if email exists or not
            return ForgotPasswordResponse.builder()
                    .success(true)
                    .message("If the email exists, a password reset link has been sent")
                    .build();
        }

        // Generate reset token
        String resetToken = UUID.randomUUID().toString();
        LocalDateTime expiryTime = LocalDateTime.now().plusHours(1); // Token expires in 1 hour

        // Save reset token
        userRepository.updateResetToken(request.getEmail(), resetToken, expiryTime);

        // Send email (simplified version)
        try {
            sendResetPasswordEmail(request.getEmail(), resetToken);
            log.info("Password reset email sent to: {}", request.getEmail());
        } catch (Exception e) {
            log.error("Failed to send password reset email", e);
            throw new RuntimeException("Failed to send password reset email");
        }

        return ForgotPasswordResponse.builder()
                .success(true)
                .message("Password reset link has been sent to your email")
                .build();
    }

    /**
     * Send password reset email
     */
    private void sendResetPasswordEmail(String email, String resetToken) {
        SimpleMailMessage message = new SimpleMailMessage();
        message.setTo(email);
        message.setSubject("Password Reset Request");
        message.setText("To reset your password, click the following link:\n\n" +
                "http://localhost:8080/api/auth/reset-password?token=" + resetToken + "\n\n" +
                "This link will expire in 1 hour.\n\n" +
                "If you did not request this password reset, please ignore this email.");

        mailSender.send(message);
    }
}
