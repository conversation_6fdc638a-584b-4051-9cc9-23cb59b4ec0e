package com.example.userauth.repository;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jooq.DSLContext;
import org.jooq.Record;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.Optional;
import java.util.UUID;

import static com.example.userauth.db.jooq.Tables.USERS;

/**
 * Repository for User entity using jOOQ
 */
@Repository
@RequiredArgsConstructor
@Slf4j
public class UserRepository {

    private final DSLContext dslContext;

    /**
     * Save a new user to the database
     */
    public UUID saveUser(String username, String email, String passwordHash, 
                        String firstName, String lastName) {
        log.debug("Saving user with username: {}", username);
        
        UUID userId = UUID.randomUUID();
        
        dslContext.insertInto(USERS)
                .set(USERS.ID, userId)
                .set(USERS.USERNAME, username)
                .set(USERS.EMAIL, email)
                .set(USERS.PASSWORD_HASH, passwordHash)
                .set(USERS.FIRST_NAME, firstName)
                .set(USERS.LAST_NAME, lastName)
                .set(USERS.IS_ACTIVE, true)
                .set(USERS.CREATED_AT, LocalDateTime.now())
                .set(USERS.UPDATED_AT, LocalDateTime.now())
                .execute();
                
        log.info("User saved successfully with ID: {}", userId);
        return userId;
    }

    /**
     * Find user by username
     */
    public Optional<Record> findByUsername(String username) {
        log.debug("Finding user by username: {}", username);
        
        Record record = dslContext.select()
                .from(USERS)
                .where(USERS.USERNAME.eq(username))
                .and(USERS.IS_ACTIVE.eq(true))
                .fetchOne();
                
        return Optional.ofNullable(record);
    }

    /**
     * Find user by email
     */
    public Optional<Record> findByEmail(String email) {
        log.debug("Finding user by email: {}", email);
        
        Record record = dslContext.select()
                .from(USERS)
                .where(USERS.EMAIL.eq(email))
                .and(USERS.IS_ACTIVE.eq(true))
                .fetchOne();
                
        return Optional.ofNullable(record);
    }

    /**
     * Find user by username or email
     */
    public Optional<Record> findByUsernameOrEmail(String usernameOrEmail) {
        log.debug("Finding user by username or email: {}", usernameOrEmail);
        
        Record record = dslContext.select()
                .from(USERS)
                .where(USERS.USERNAME.eq(usernameOrEmail)
                        .or(USERS.EMAIL.eq(usernameOrEmail)))
                .and(USERS.IS_ACTIVE.eq(true))
                .fetchOne();
                
        return Optional.ofNullable(record);
    }

    /**
     * Find user by ID
     */
    public Optional<Record> findById(UUID userId) {
        log.debug("Finding user by ID: {}", userId);
        
        Record record = dslContext.select()
                .from(USERS)
                .where(USERS.ID.eq(userId))
                .and(USERS.IS_ACTIVE.eq(true))
                .fetchOne();
                
        return Optional.ofNullable(record);
    }

    /**
     * Check if username exists
     */
    public boolean existsByUsername(String username) {
        log.debug("Checking if username exists: {}", username);
        
        return dslContext.fetchExists(
                dslContext.selectOne()
                        .from(USERS)
                        .where(USERS.USERNAME.eq(username))
        );
    }

    /**
     * Check if email exists
     */
    public boolean existsByEmail(String email) {
        log.debug("Checking if email exists: {}", email);
        
        return dslContext.fetchExists(
                dslContext.selectOne()
                        .from(USERS)
                        .where(USERS.EMAIL.eq(email))
        );
    }

    /**
     * Update reset token for forgot password
     */
    public void updateResetToken(String email, String resetToken, LocalDateTime expiryTime) {
        log.debug("Updating reset token for email: {}", email);
        
        int updatedRows = dslContext.update(USERS)
                .set(USERS.RESET_TOKEN, resetToken)
                .set(USERS.RESET_TOKEN_EXPIRY, expiryTime)
                .set(USERS.UPDATED_AT, LocalDateTime.now())
                .where(USERS.EMAIL.eq(email))
                .execute();
                
        log.info("Updated reset token for {} rows", updatedRows);
    }

    /**
     * Find user by reset token
     */
    public Optional<Record> findByResetToken(String resetToken) {
        log.debug("Finding user by reset token");
        
        Record record = dslContext.select()
                .from(USERS)
                .where(USERS.RESET_TOKEN.eq(resetToken))
                .and(USERS.RESET_TOKEN_EXPIRY.greaterThan(LocalDateTime.now()))
                .and(USERS.IS_ACTIVE.eq(true))
                .fetchOne();
                
        return Optional.ofNullable(record);
    }

    /**
     * Clear reset token after password reset
     */
    public void clearResetToken(UUID userId) {
        log.debug("Clearing reset token for user ID: {}", userId);
        
        dslContext.update(USERS)
                .setNull(USERS.RESET_TOKEN)
                .setNull(USERS.RESET_TOKEN_EXPIRY)
                .set(USERS.UPDATED_AT, LocalDateTime.now())
                .where(USERS.ID.eq(userId))
                .execute();
    }
}
