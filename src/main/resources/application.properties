# Default application properties
spring.application.name=user-auth-service
server.port=8081

# Database configuration
spring.datasource.url=*****************************************
spring.datasource.username=postgres
spring.datasource.password=password
spring.datasource.driver-class-name=org.postgresql.Driver

# JPA/Hibernate configuration
spring.jpa.hibernate.ddl-auto=none
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect

# Liquibase configuration
spring.liquibase.change-log=classpath:db/changelog/db.changelog-master.yml

# JWT configuration
jwt.secret=mySecretKey123456789012345678901234567890
jwt.expiration=86400000

# Mail configuration (for forgot password)
spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.username=<EMAIL>
spring.mail.password=your-app-password
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true

# Logging
logging.level.com.example.userauth=DEBUG
logging.level.org.jooq=DEBUG
